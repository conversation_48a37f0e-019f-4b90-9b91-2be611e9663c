/**
 * Overlay management for video compositing (overlay video and logo)
 */

export interface OverlayManagerOptions {
  addLog: (level: 'info' | 'warn' | 'error' | 'debug' | 'success', category: string, message: string, data?: any) => void;
}

export class OverlayManager {
  private overlayVideoRef: HTMLVideoElement | null = null;
  private logoImageRef: HTMLImageElement | null = null;
  private addLog: (level: 'info' | 'warn' | 'error' | 'debug' | 'success', category: string, message: string, data?: any) => void;

  constructor(options: OverlayManagerOptions) {
    this.addLog = options.addLog;
  }

  /**
   * Set up overlay video
   */
  setupOverlayVideo(overlayVideoUrl: string): void {
    if (!this.overlayVideoRef) {
      const video = document.createElement('video');
      video.autoplay = true;
      video.playsInline = true;
      video.muted = true;
      video.loop = true;
      video.crossOrigin = 'anonymous';
      video.style.position = 'absolute';
      video.style.top = '0px';
      video.style.left = '0px';
      video.style.width = '1px';
      video.style.height = '1px';
      video.style.opacity = '0.01';
      video.style.pointerEvents = 'none';
      video.style.zIndex = '-1000';

      // Add to DOM to ensure proper loading
      document.body.appendChild(video);

      this.overlayVideoRef = video;
      // this.addLog('info', 'Compositor', 'Created overlay video element');
    }

    if (this.overlayVideoRef.src !== overlayVideoUrl) {
      const video = this.overlayVideoRef;
      video.src = overlayVideoUrl;

      // Add event listeners for overlay video
      video.addEventListener('loadstart', () => {
        // this.addLog('info', 'Compositor', 'Overlay video load started');
      });

      video.addEventListener('loadeddata', () => {
        // this.addLog('success', 'Compositor', 'Overlay video data loaded');
      });

      video.addEventListener('canplay', () => {
        // this.addLog('success', 'Compositor', `Overlay video can play - dimensions: ${video.videoWidth}x${video.videoHeight}`);
      });

      video.addEventListener('error', (e) => {
        // this.addLog('error', 'Compositor', 'Overlay video error', e);
      });

      // Force video to load and play
      video.load();
      video.play().then(() => {
        // this.addLog('success', 'Compositor', 'Overlay video started playing');
      }).catch(error => {
        // this.addLog('error', 'Compositor', 'Failed to play overlay video', error);
      });

      // this.addLog('success', 'Compositor', `Assigned overlay video URL: ${overlayVideoUrl}`);

      // Add additional debugging
      setTimeout(() => {
        // this.addLog('debug', 'Compositor', `Overlay video status after 2s: readyState=${video.readyState}, networkState=${video.networkState}, dimensions=${video.videoWidth}x${video.videoHeight}`);
      }, 2000);
    }
  }

  /**
   * Remove overlay video
   */
  removeOverlayVideo(): void {
    if (this.overlayVideoRef) {
      if (this.overlayVideoRef.parentNode) {
        this.overlayVideoRef.parentNode.removeChild(this.overlayVideoRef);
      }
      this.overlayVideoRef.src = '';
      this.overlayVideoRef = null;
      // this.addLog('info', 'Compositor', 'Removed overlay video element');
    }
  }

  /**
   * Set up logo image
   */
  setupLogoImage(): void {
    // this.addLog('info', 'Compositor', 'setupLogoImage called');

    if (!this.logoImageRef) {
      // this.addLog('info', 'Compositor', 'Creating new logo image element');

      const image = new Image();
      image.crossOrigin = 'anonymous';
      image.style.position = 'absolute';
      image.style.top = '0px';
      image.style.left = '0px';
      image.style.width = '1px';
      image.style.height = '1px';
      image.style.opacity = '0.01';
      image.style.pointerEvents = 'none';
      image.style.zIndex = '-1000';

      // Add to DOM to ensure proper loading
      document.body.appendChild(image);

      this.logoImageRef = image;
      // this.addLog('success', 'Compositor', 'Created logo image element and added to DOM');

      // Load the SVG logo
      image.onload = () => {
        // this.addLog('success', 'Compositor', `Logo image loaded successfully - dimensions: ${image.naturalWidth}x${image.naturalHeight}, complete: ${image.complete}`);
      };

      image.onerror = (e) => {
        // this.addLog('error', 'Compositor', 'Logo image load error', e);
      };

      // this.addLog('info', 'Compositor', 'Starting to load and convert SVG logo');

      // Load and convert SVG logo for WebGL compatibility
      // this.addLog('info', 'Compositor', 'Starting SVG to bitmap conversion for WebGL compatibility');
      this.loadAndConvertSVGLogo(image);
    } else {
      // this.addLog('info', 'Compositor', 'Logo image element already exists');
    }
  }

  /**
   * Create a simple test logo to verify the rendering pipeline
   */
  private createTestLogo(image: HTMLImageElement): void {
    // this.addLog('info', 'Compositor', 'Creating simple test logo');

    // Create a canvas to draw a simple logo
    const canvas = document.createElement('canvas');
    canvas.width = 128;
    canvas.height = 128;
    const ctx = canvas.getContext('2d');

    if (ctx) {
      // Clear canvas with transparent background
      ctx.clearRect(0, 0, 128, 128);

      // Draw a simple logo - blue circle with white "S"
      ctx.fillStyle = '#00d4ff';
      ctx.beginPath();
      ctx.arc(64, 64, 50, 0, 2 * Math.PI);
      ctx.fill();

      // Add white text
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 48px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('S', 64, 64);

      // Convert canvas to data URL and set as image source
      const dataUrl = canvas.toDataURL('image/png');
      image.src = dataUrl;
      // this.addLog('success', 'Compositor', `Test logo created successfully, data URL length: ${dataUrl.length}`);
    } else {
      // this.addLog('error', 'Compositor', 'Failed to get canvas 2D context for test logo');
    }
  }

  /**
   * Load and convert SVG logo to bitmap
   */
  private loadAndConvertSVGLogo(image: HTMLImageElement): void {
    // this.addLog('info', 'Compositor', 'Starting SVG fetch from /switcher-logo.svg');

    fetch('/switcher-logo.svg')
      .then(response => {
        // this.addLog('info', 'Compositor', `SVG fetch response: ${response.status} ${response.statusText}`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
      })
      .then(svgText => {
        // this.addLog('info', 'Compositor', `SVG text loaded, length: ${svgText.length}`);

        // Create a blob from the SVG text
        const blob = new Blob([svgText], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);

        // this.addLog('info', 'Compositor', `Created blob URL: ${url}`);

        // Create a temporary image to render the SVG
        const tempImg = new Image();
        tempImg.onload = () => {
          // this.addLog('info', 'Compositor', `Temp image loaded: ${tempImg.width}x${tempImg.height}`);

          // Create a canvas to convert SVG to bitmap
          const canvas = document.createElement('canvas');
          canvas.width = 128;
          canvas.height = 128;
          const ctx = canvas.getContext('2d');

          if (ctx) {
            // Clear canvas with transparent background
            ctx.clearRect(0, 0, 128, 128);

            // Draw the SVG image onto the canvas
            ctx.drawImage(tempImg, 0, 0, 128, 128);

            // Convert canvas to data URL and set as image source
            const dataUrl = canvas.toDataURL('image/png');
            image.src = dataUrl;
            // this.addLog('success', 'Compositor', `SVG logo converted to bitmap successfully, data URL length: ${dataUrl.length}`);
          } else {
            // this.addLog('error', 'Compositor', 'Failed to get canvas 2D context');
          }

          // Clean up the blob URL
          URL.revokeObjectURL(url);
        };

        tempImg.onerror = (e) => {
          // this.addLog('error', 'Compositor', 'Failed to load SVG for conversion', e);
          URL.revokeObjectURL(url);
        };

        tempImg.src = url;
      })
      .catch(error => {
        // this.addLog('error', 'Compositor', 'Failed to fetch SVG file', error);
      });
  }

  /**
   * Remove logo image
   */
  removeLogoImage(): void {
    if (this.logoImageRef && this.logoImageRef.parentNode) {
      this.logoImageRef.parentNode.removeChild(this.logoImageRef);
      this.logoImageRef = null;
      // this.addLog('info', 'Compositor', 'Removed logo image element');
    }
  }

  /**
   * Get overlay video element
   */
  getOverlayVideo(): HTMLVideoElement | null {
    return this.overlayVideoRef;
  }

  /**
   * Get logo image element
   */
  getLogoImage(): HTMLImageElement | null {
    return this.logoImageRef;
  }

  /**
   * Check if overlay video is ready for rendering
   */
  isOverlayVideoReady(): boolean {
    return !!(this.overlayVideoRef &&
      this.overlayVideoRef.readyState >= this.overlayVideoRef.HAVE_CURRENT_DATA &&
      this.overlayVideoRef.videoWidth > 0 &&
      this.overlayVideoRef.videoHeight > 0);
  }

  /**
   * Check if logo image is ready for rendering
   */
  isLogoImageReady(): boolean {
    return !!(this.logoImageRef && 
      this.logoImageRef.complete && 
      this.logoImageRef.naturalWidth > 0);
  }

  /**
   * Clean up all overlay elements
   */
  cleanup(): void {
    this.removeOverlayVideo();
    this.removeLogoImage();
  }
}
