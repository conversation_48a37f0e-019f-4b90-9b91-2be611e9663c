/**
 * Video element management for WebRTC streams
 */

export interface VideoElementManagerOptions {
  addLog: (level: 'info' | 'warn' | 'error' | 'debug' | 'success', category: string, message: string, data?: any) => void;
}

export class VideoElementManager {
  private videoRefs: Record<string, HTMLVideoElement> = {};
  private localVideoRef: HTMLVideoElement | null = null;
  private overlayVideoRef: HTMLVideoElement | null = null;
  private logoImageRef: HTMLImageElement | null = null;
  private addLog: (level: 'info' | 'warn' | 'error' | 'debug' | 'success', category: string, message: string, data?: any) => void;

  constructor(options: VideoElementManagerOptions) {
    this.addLog = options.addLog;
  }

  /**
   * Create or get local video element
   */
  getOrCreateLocalVideo(stream: MediaStream): HTMLVideoElement {
    if (!this.localVideoRef) {
      const video = document.createElement('video');
      video.autoplay = true;
      video.playsInline = true;
      video.muted = true;
      video.style.position = 'absolute';
      video.style.top = '0px';
      video.style.left = '0px';
      video.style.width = '1px';
      video.style.height = '1px';
      video.style.opacity = '0.01'; // Nearly invisible but still rendered
      video.style.pointerEvents = 'none';
      video.style.zIndex = '-1000';

      // Add to DOM to ensure proper loading
      document.body.appendChild(video);

      this.localVideoRef = video;
      // this.addLog('info', 'Compositor', 'Created local video element for compositor');
    }

    if (this.localVideoRef.srcObject !== stream) {
      const video = this.localVideoRef;
      video.srcObject = stream;

      // Force video to load and play
      video.load();
      video.play().then(() => {
        // this.addLog('success', 'Compositor', 'Local video started playing');
      }).catch(error => {
        // this.addLog('error', 'Compositor', 'Failed to play local video', error);
      });

      // this.addLog('success', 'Compositor', 'Assigned local stream to compositor video element');
    }

    return this.localVideoRef;
  }

  /**
   * Remove local video element
   */
  removeLocalVideo(): void {
    if (this.localVideoRef) {
      if (this.localVideoRef.parentNode) {
        this.localVideoRef.parentNode.removeChild(this.localVideoRef);
      }
      this.localVideoRef.srcObject = null;
      this.localVideoRef = null;
      // this.addLog('info', 'Compositor', 'Removed local video element from compositor');
    }
  }

  /**
   * Get or create peer video element
   */
  getOrCreatePeerVideo(peerId: string, peer: any): HTMLVideoElement {
    if (!this.videoRefs[peerId]) {
      const video = document.createElement('video');
      video.autoplay = true;
      video.playsInline = true;
      video.muted = true;
      video.style.position = 'absolute';
      video.style.top = '10px';
      video.style.right = '10px';
      video.style.width = '160px';
      video.style.height = '120px';
      video.style.border = '2px solid #8B5CF6';
      video.style.borderRadius = '8px';
      video.style.zIndex = '1000';
      video.style.pointerEvents = 'none';

      // Add to DOM to ensure proper loading
      document.body.appendChild(video);

      this.videoRefs[peerId] = video;
      // this.addLog('info', 'Compositor', `Created video element for peer ${peerId}`);
    }

    // Update stream if it exists and is different
    if (peer.stream && this.videoRefs[peerId].srcObject !== peer.stream) {
      this.assignStreamToPeerVideo(peerId, peer);
    }

    return this.videoRefs[peerId];
  }

  /**
   * Assign stream to peer video with comprehensive error handling
   */
  private assignStreamToPeerVideo(peerId: string, peer: any): void {
    const video = this.videoRefs[peerId];
    
    // Check if stream is active
    if (!peer.stream.active) {
      // this.addLog('warn', 'Compositor', `Stream for peer ${peerId} is not active`, {
      //   streamId: peer.stream.id,
      //   active: peer.stream.active,
      //   tracks: peer.stream.getTracks().map((t: MediaStreamTrack) => ({
      //     kind: t.kind,
      //     enabled: t.enabled,
      //     readyState: t.readyState
      //   }))
      // });
    }

    // Check if any tracks are muted or not live
    const tracks = peer.stream.getTracks();
    tracks.forEach((track: MediaStreamTrack) => {
      if (track.readyState !== 'live') {
        // this.addLog('warn', 'Compositor', `Track not live for peer ${peerId}`, {
        //   trackKind: track.kind,
        //   trackId: track.id,
        //   readyState: track.readyState,
        //   enabled: track.enabled
        // });
      }
    });

    video.srcObject = peer.stream;

    // Add comprehensive event listeners to track video loading
    this.addVideoEventListeners(video, peerId);

    // Force video to load and play with multiple attempts
    this.attemptVideoPlay(video, peerId, peer);
  }

  /**
   * Add event listeners to video element
   */
  private addVideoEventListeners(video: HTMLVideoElement, peerId: string): void {
    video.addEventListener('loadstart', () => {
      // this.addLog('info', 'Compositor', `Video load started for peer ${peerId}`);
    });

    video.addEventListener('loadedmetadata', () => {
      // this.addLog('success', 'Compositor', `Video metadata loaded for peer ${peerId}`, {
      //   dimensions: `${video.videoWidth}x${video.videoHeight}`,
      //   duration: video.duration,
      //   readyState: video.readyState
      // });
    });

    video.addEventListener('loadeddata', () => {
      // this.addLog('success', 'Compositor', `Video data loaded for peer ${peerId}`, {
      //   readyState: video.readyState
      // });
    });

    video.addEventListener('canplay', () => {
      // this.addLog('success', 'Compositor', `Video can play for peer ${peerId}`, {
      //   readyState: video.readyState
      // });
    });

    video.addEventListener('canplaythrough', () => {
      // this.addLog('success', 'Compositor', `Video can play through for peer ${peerId}`, {
      //   readyState: video.readyState
      // });
    });

    video.addEventListener('error', (e) => {
      // this.addLog('error', 'Compositor', `Video error for peer ${peerId}`, {
      //   error: e,
      //   networkState: video.networkState,
      //   readyState: video.readyState
      // });
    });

    video.addEventListener('stalled', () => {
      // this.addLog('warn', 'Compositor', `Video stalled for peer ${peerId}`, {
      //   networkState: video.networkState,
      //   readyState: video.readyState
      // });
    });

    video.addEventListener('waiting', () => {
      // this.addLog('warn', 'Compositor', `Video waiting for peer ${peerId}`, {
      //   networkState: video.networkState,
      //   readyState: video.readyState
      // });
    });
  }

  /**
   * Attempt to play video with recovery mechanisms
   */
  private async attemptVideoPlay(video: HTMLVideoElement, peerId: string, peer: any): Promise<void> {
    try {
      // Ensure video properties are set correctly
      video.autoplay = true;
      video.playsInline = true;
      video.muted = true;

      // Try different approaches to get the video to play
      video.load();

      // Wait a bit for the stream to be ready
      await new Promise(resolve => setTimeout(resolve, 100));

      await video.play();
      // this.addLog('success', 'Compositor', `Video started playing for peer ${peerId}`);
    } catch (error) {
      // this.addLog('error', 'Compositor', `Failed to play video for peer ${peerId}`, error);
      this.setupVideoRecovery(video, peerId, peer);
    }
  }

  /**
   * Set up video recovery mechanisms
   */
  private setupVideoRecovery(video: HTMLVideoElement, peerId: string, peer: any): void {
    // Set up periodic checks to monitor video loading with escalating recovery
    let checkCount = 0;
    const checkInterval = setInterval(() => {
      checkCount++;

      if (video.readyState === 0 && video.srcObject) {
        // this.addLog('warn', 'Compositor', `Video still not loading for peer ${peerId} (attempt ${checkCount})`, {
        //   readyState: video.readyState,
        //   networkState: video.networkState,
        //   hasStream: !!video.srcObject,
        //   streamActive: video.srcObject ? (video.srcObject as MediaStream).active : false,
        //   videoWidth: video.videoWidth,
        //   videoHeight: video.videoHeight
        // });

        // Escalating recovery attempts
        if (checkCount === 3) {
          // After 6 seconds, try resetting srcObject
          // this.addLog('info', 'Compositor', `Attempting srcObject reset for peer ${peerId}`);
          const currentStream = video.srcObject;
          video.srcObject = null;
          setTimeout(() => {
            video.srcObject = currentStream;
            video.load();
            video.play(); //.catch(e => // this.addLog('warn', 'Compositor', `Reset play failed for peer ${peerId}`, e));
          }, 200);
        } else if (checkCount === 6) {
          // After 12 seconds, try the nuclear option - complete recreation
          this.recreateVideoElement(peerId, peer);
        }
      } else if (video.readyState >= 2) {
        // Video is loading/loaded, clear the interval
        clearInterval(checkInterval);
        // this.addLog('success', 'Compositor', `Video loading progressed for peer ${peerId}`, {
        //   readyState: video.readyState,
        //   dimensions: `${video.videoWidth}x${video.videoHeight}`
        // });
      }
    }, 2000);

    // Clear interval after 30 seconds to avoid memory leaks
    setTimeout(() => {
      clearInterval(checkInterval);
    }, 30000);
  }

  /**
   * Recreate video element as last resort
   */
  private recreateVideoElement(peerId: string, peer: any): void {
    // this.addLog('warn', 'Compositor', `Attempting complete video element recreation for peer ${peerId}`);

    const oldVideo = this.videoRefs[peerId];
    if (oldVideo && oldVideo.parentNode) {
      oldVideo.parentNode.removeChild(oldVideo);
    }

    const newVideo = document.createElement('video');
    newVideo.autoplay = true;
    newVideo.playsInline = true;
    newVideo.muted = true;
    newVideo.setAttribute('playsinline', 'true');
    newVideo.setAttribute('webkit-playsinline', 'true');
    newVideo.controls = false;
    newVideo.style.position = 'absolute';
    newVideo.style.top = '0px';
    newVideo.style.left = '0px';
    newVideo.style.width = '1px';
    newVideo.style.height = '1px';
    newVideo.style.opacity = '0.01';
    newVideo.style.pointerEvents = 'none';
    newVideo.style.zIndex = '-1000';

    document.body.appendChild(newVideo);
    this.videoRefs[peerId] = newVideo;

    if (peer.stream) {
      newVideo.srcObject = peer.stream;
      newVideo.load();
      newVideo.play(); //.catch(e => this.addLog('error', 'Compositor', `Recreation play failed for peer ${peerId}`, e));
    }
  }

  /**
   * Remove peer video element
   */
  removePeerVideo(peerId: string): void {
    const video = this.videoRefs[peerId];
    if (video && video.parentNode) {
      video.parentNode.removeChild(video);
    }
    delete this.videoRefs[peerId];
    // this.addLog('info', 'Compositor', `Removed video element for peer ${peerId}`);
  }

  /**
   * Get all video elements
   */
  getAllVideos(): { local: HTMLVideoElement | null; peers: Record<string, HTMLVideoElement> } {
    return {
      local: this.localVideoRef,
      peers: { ...this.videoRefs }
    };
  }

  /**
   * Get ready videos for rendering
   */
  getReadyVideos(): HTMLVideoElement[] {
    const readyVideos: HTMLVideoElement[] = [];

    // Add local video if ready
    if (this.localVideoRef && this.localVideoRef.readyState >= this.localVideoRef.HAVE_CURRENT_DATA) {
      readyVideos.push(this.localVideoRef);
    }

    // Add peer videos that are ready
    Object.values(this.videoRefs).forEach(video => {
      if (video.readyState >= video.HAVE_CURRENT_DATA && video.videoWidth > 0 && video.videoHeight > 0) {
        readyVideos.push(video);
      }
    });

    return readyVideos;
  }

  /**
   * Get video ID for a video element
   */
  getVideoId(video: HTMLVideoElement): string {
    if (video === this.localVideoRef) {
      return 'local';
    }

    for (const [peerId, peerVideo] of Object.entries(this.videoRefs)) {
      if (peerVideo === video) {
        return peerId;
      }
    }

    return 'unknown';
  }

  /**
   * Clean up all video elements
   */
  cleanup(): void {
    // Clean up local video
    this.removeLocalVideo();

    // Clean up peer videos
    Object.keys(this.videoRefs).forEach(peerId => {
      this.removePeerVideo(peerId);
    });

    // Clean up overlay video
    if (this.overlayVideoRef) {
      if (this.overlayVideoRef.parentNode) {
        this.overlayVideoRef.parentNode.removeChild(this.overlayVideoRef);
      }
      this.overlayVideoRef.src = '';
      this.overlayVideoRef = null;
    }

    // Clean up logo image
    if (this.logoImageRef && this.logoImageRef.parentNode) {
      this.logoImageRef.parentNode.removeChild(this.logoImageRef);
      this.logoImageRef = null;
    }
  }
}
