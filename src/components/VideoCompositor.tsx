import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useLog } from '../contexts/LogContext';
import {
  LayoutMode,
  calculateVideoViewport,
  calculateOverlayViewport,
  calculateLogoViewport
} from './webgl/layoutUtils';
import { WebGLEngine } from './webgl/WebGLEngine';
import { VideoElementManager } from './video/VideoElementManager';
import { OverlayManager } from './video/OverlayManager';
import { LayoutControls } from './ui/LayoutControls';
import { StatusOverlay } from './ui/StatusOverlay';

interface VideoCompositorProps {
  peers: Record<string, any>;
  localStream: MediaStream | null;
  overlayVideoUrl?: string;
  showOverlays?: boolean;
  layoutMode?: LayoutMode;
  onLayoutModeChange?: (mode: LayoutMode) => void;
  onDebugVideoState?: () => void;
}



export const VideoCompositor: React.FC<VideoCompositorProps> = ({
  peers,
  localStream,
  overlayVideoUrl,
  showOverlays = true,
  layoutMode: externalLayoutMode = 'grid',
  onLayoutModeChange,
  onDebugVideoState
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [internalLayoutMode, setInternalLayoutMode] = useState<LayoutMode>('grid');
  const [focusedPeer, setFocusedPeer] = useState<string>('');

  // Use external layout mode if provided, otherwise use internal state
  const layoutMode = showOverlays ? internalLayoutMode : externalLayoutMode;

  const webglEngineRef = useRef<WebGLEngine | null>(null);
  const videoManagerRef = useRef<VideoElementManager | null>(null);
  const overlayManagerRef = useRef<OverlayManager | null>(null);
  const { addLog } = useLog();

  // Initialize managers
  useEffect(() => {
    // addLog('info', 'Compositor', 'Initializing managers...');

    if (!videoManagerRef.current) {
      // addLog('info', 'Compositor', 'Creating VideoElementManager');
      videoManagerRef.current = new VideoElementManager({ addLog });
    }

    if (!overlayManagerRef.current) {
      // addLog('info', 'Compositor', 'Creating OverlayManager');
      overlayManagerRef.current = new OverlayManager({ addLog });

      // Set up logo image immediately
      // addLog('info', 'Compositor', 'Setting up logo image');
      overlayManagerRef.current.setupLogoImage();
      // addLog('info', 'Compositor', 'Logo image setup initiated');
    }

    // addLog('success', 'Compositor', 'Managers initialized');
  }, [addLog]);

  // Debug function to log current state
  const debugVideoState = useCallback(() => {
    addLog('info', 'Debug', '=== VIDEO COMPOSITOR DEBUG STATE ===');
    addLog('info', 'Debug', `Total peers: ${Object.keys(peers).length}`);

    // Log peer details
    Object.entries(peers).forEach(([peerId, peer]) => {
      addLog('info', 'Debug', `Peer ${peerId}:`, {
        hasConnection: !!peer.connection,
        hasStream: !!peer.stream,
        streamId: peer.stream?.id,
        connectionState: peer.connection?.connectionState,
        iceConnectionState: peer.connection?.iceConnectionState,
        signalingState: peer.connection?.signalingState,
        streamTracks: peer.stream?.getTracks().map((t: MediaStreamTrack) => ({
          kind: t.kind,
          id: t.id,
          enabled: t.enabled,
          readyState: t.readyState,
          muted: t.muted
        })) || []
      });
    });

    // Log video element details
    const videoManager = videoManagerRef.current;
    if (videoManager) {
      const { local, peers } = videoManager.getAllVideos();
      addLog('info', 'Debug', `Video elements: ${Object.keys(peers).length} peers + ${local ? 1 : 0} local`);

      Object.entries(peers).forEach(([peerId, video]) => {
        addLog('info', 'Debug', `Video element ${peerId}:`, {
          readyState: video.readyState,
          dimensions: `${video.videoWidth}x${video.videoHeight}`,
          hasStream: !!video.srcObject,
          streamId: (video.srcObject as MediaStream)?.id,
          paused: video.paused,
          ended: video.ended,
          currentTime: video.currentTime
        });
      });

      // Log local video
      if (local) {
        addLog('info', 'Debug', 'Local video:', {
          readyState: local.readyState,
          dimensions: `${local.videoWidth}x${local.videoHeight}`,
          hasStream: !!local.srcObject,
          streamId: (local.srcObject as MediaStream)?.id,
          paused: local.paused,
          ended: local.ended
        });
      } else {
        addLog('info', 'Debug', 'No local video element');
      }
    } else {
      addLog('info', 'Debug', 'No video manager available');
    }

    // Log WebGL state
    const engine = webglEngineRef.current;
    addLog('info', 'Debug', 'WebGL state:', {
      hasEngine: !!engine,
      textureCount: engine?.getTextureCount() || 0,
      textureKeys: engine?.getTextureKeys() || []
    });

    addLog('info', 'Debug', '=== END DEBUG STATE ===');
  }, [peers, addLog]);





  const initWebGL = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) {
      // addLog('error', 'WebGL', 'Canvas element not found');
      return false;
    }

    // addLog('info', 'WebGL', `Canvas found: ${canvas.width}x${canvas.height}`);

    if (!webglEngineRef.current) {
      // addLog('info', 'WebGL', 'Creating new WebGL engine');
      webglEngineRef.current = new WebGLEngine(addLog);
    }

    const result = webglEngineRef.current.initialize(canvas);
    // addLog('info', 'WebGL', `WebGL engine initialization result: ${result}`);
    return result;
  }, [addLog]);









  // Handle overlay video
  useEffect(() => {
    const overlayManager = overlayManagerRef.current;
    if (!overlayManager) return;

    if (overlayVideoUrl) {
      overlayManager.setupOverlayVideo(overlayVideoUrl);
    } else {
      overlayManager.removeOverlayVideo();
    }
  }, [overlayVideoUrl]);



  // Handle local stream
  useEffect(() => {
    const videoManager = videoManagerRef.current;
    if (!videoManager) return;

    if (localStream) {
      videoManager.getOrCreateLocalVideo(localStream);
    } else {
      videoManager.removeLocalVideo();
    }
  }, [localStream]);

  // Handle peer streams
  useEffect(() => {
    const videoManager = videoManagerRef.current;
    if (!videoManager) return;

    const peerCount = Object.keys(peers).length;
    const peersWithStreams = Object.values(peers).filter(peer => peer.stream).length;

    // Always log when peers change
    // addLog('info', 'Compositor', `Peers updated: ${peerCount} total, ${peersWithStreams} with streams`, {
    //   peerIds: Object.keys(peers),
    //   peersWithStreams: Object.entries(peers)
    //     .filter(([_, peer]) => peer.stream)
    //     .map(([id, _]) => id)
    // });

    // Create or update video elements for each peer
    Object.entries(peers).forEach(([peerId, peer]) => {
      videoManager.getOrCreatePeerVideo(peerId, peer);
    });

    // Clean up removed peers
    const { peers: currentPeerVideos } = videoManager.getAllVideos();
    Object.keys(currentPeerVideos).forEach(peerId => {
      if (!peers[peerId]) {
        videoManager.removePeerVideo(peerId);
      }
    });
  }, [peers]);



  // Initialize WebGL on mount
  useEffect(() => {
    // addLog('info', 'WebGL', 'Initializing WebGL...');
    if (!initWebGL()) {
      // addLog('error', 'WebGL', 'Failed to initialize WebGL, testing 2D canvas fallback');

      // Test 2D canvas as fallback
      const canvas = canvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext('2d');
        if (ctx) {
          // addLog('info', 'WebGL', 'Testing 2D canvas fallback');
          ctx.fillStyle = '#ff00ff'; // Magenta
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          // addLog('success', 'WebGL', '2D canvas fallback working');
        } else {
          // addLog('error', 'WebGL', '2D canvas context also failed');
        }
      }
      return;
    }
    // addLog('success', 'WebGL', 'WebGL initialized successfully');
  }, [initWebGL, addLog]);

  // Main rendering loop
  useEffect(() => {
    const canvas = canvasRef.current;
    const engine = webglEngineRef.current;

    // addLog('info', 'WebGL', 'Setting up rendering loop', {
    //   hasCanvas: !!canvas,
    //   hasEngine: !!engine
    // });

    if (!canvas) {
      // addLog('error', 'WebGL', 'No canvas found for rendering');
      return;
    }

    if (!engine) {
      // addLog('error', 'WebGL', 'No WebGL engine found for rendering');
      return;
    }

    const draw = () => {
      const videoManager = videoManagerRef.current;
      const engine = webglEngineRef.current;

      if (!videoManager) {
        // addLog('warn', 'WebGL', 'No video manager in draw function');
        return;
      }

      if (!engine) {
        // addLog('warn', 'WebGL', 'No WebGL engine in draw function');
        return;
      }

      // Log that draw function is being called
      if (Math.random() < 0.01) { // Log 1% of draw calls
        // addLog('debug', 'WebGL', 'Draw function called with engine and video manager');
      }

      // Collect all available videos (local + peers)
      const allVideos = videoManager.getReadyVideos();

      // Log occasionally to track video rendering
      if (Math.random() < 0.002) { // Log ~0.2% of frames for debugging
        const { local, peers } = videoManager.getAllVideos();
        // addLog('debug', 'Compositor', `Drawing ${allVideos.length} videos`, {
        //   localVideoReady: local ? local.readyState >= local.HAVE_CURRENT_DATA : false,
        //   localVideoDimensions: local ? `${local.videoWidth}x${local.videoHeight}` : 'none',
        //   peerVideoDetails: Object.entries(peers).map(([peerId, video]) => ({
        //     peerId,
        //     readyState: video.readyState,
        //     dimensions: `${video.videoWidth}x${video.videoHeight}`,
        //     hasStream: !!video.srcObject
        //   }))
        // });
      }

      if (allVideos.length === 0) {
        // Clear the canvas with gray background
        engine.clear(0.22, 0.25, 0.31, 1.0);
      } else {
        // Clear the canvas for video rendering
        engine.clear(0.0, 0.0, 0.0, 1.0);
      }

      // Update textures and render videos
      allVideos.forEach((video, index) => {
        // Determine video ID using the video manager
        const videoId = videoManager.getVideoId(video);

        // Log video processing details occasionally
        if (Math.random() < 0.001) {
          // addLog('debug', 'WebGL', `Processing video ${index}: ${videoId} (readyState: ${video.readyState}, dimensions: ${video.videoWidth}x${video.videoHeight})`);
        }

        // Get or create texture for this video
        let texture = engine.getTexture(videoId);
        if (!texture) {
          const newTexture = engine.createVideoTexture(videoId, video);
          if (newTexture) {
            texture = newTexture;
            // addLog('success', 'WebGL', `Created texture for video ${videoId}`);
          } else {
            // addLog('error', 'WebGL', `Failed to create texture for video ${videoId}`);
            return;
          }
        }

        if (texture && video.videoWidth > 0 && video.videoHeight > 0) {
          try {
            // Update texture with current video frame
            engine.updateVideoTexture(texture, video);

            // Calculate viewport based on layout
            const viewport = calculateVideoViewport(index, allVideos.length, canvas.width, canvas.height, layoutMode);

            // Render the video
            const borderColor: [number, number, number] = videoId === 'local' ? [0, 0.83, 1] : [0.54, 0.31, 0.96]; // Blue for local, purple for peers
            engine.renderVideo(texture, viewport, borderColor);

            // Log rendering success occasionally
            if (Math.random() < 0.001) {
              // addLog('debug', 'WebGL', `Rendered video ${videoId} at viewport (${viewport.x}, ${viewport.y}, ${viewport.width}, ${viewport.height})`);
            }
          } catch (error) {
            // addLog('error', 'WebGL', `Failed to render video ${videoId}`, error);
          }
        } else {
          // Log readiness issues occasionally
          if (Math.random() < 0.005) {
            // addLog('warn', 'WebGL', `Video ${videoId} not ready for rendering (readyState: ${video.readyState}, dimensions: ${video.videoWidth}x${video.videoHeight}, hasTexture: ${!!texture})`);
          }
        }
      });

      // Render overlay video on top if available
      const overlayManager = overlayManagerRef.current;
      if (overlayManager && overlayManager.isOverlayVideoReady()) {
        const overlayVideo = overlayManager.getOverlayVideo()!;
        const overlayVideoId = 'overlay';

        // Get or create texture for overlay video
        let overlayTexture = engine.getTexture(overlayVideoId);
        if (!overlayTexture) {
          const newTexture = engine.createVideoTexture(overlayVideoId, overlayVideo);
          if (newTexture) {
            overlayTexture = newTexture;
            // addLog('success', 'WebGL', 'Created texture for overlay video');
          } else {
            // addLog('error', 'WebGL', 'Failed to create texture for overlay video');
          }
        }

        if (overlayTexture) {
          try {
            // Update texture with current overlay video frame
            engine.updateVideoTexture(overlayTexture, overlayVideo);

            // Calculate overlay viewport (bottom-right, 50% width and height)
            const overlayViewport = calculateOverlayViewport(canvas.width, canvas.height);

            // Render the overlay video with a distinct border color (green)
            engine.renderVideo(overlayTexture, overlayViewport, [0, 1, 0.5], 1.0); // Green border, full opacity

            // Log overlay rendering success occasionally
            if (Math.random() < 0.001) {
              // addLog('debug', 'WebGL', `Rendered overlay video at viewport (${overlayViewport.x}, ${overlayViewport.y}, ${overlayViewport.width}, ${overlayViewport.height}) on canvas ${canvas.width}x${canvas.height}`);
            }
          } catch (error) {
            // addLog('error', 'WebGL', 'Failed to render overlay video', error);
          }
        }
      }

      // Always try to render logo image on top if available (even with no videos)
      if (overlayManager) {
        // Test if overlay manager is working
        if (Math.random() < 0.1) { // Log 10% of frames
          // addLog('debug', 'WebGL', 'OverlayManager exists, testing logo...');
        }

        const logoImage = overlayManager.getLogoImage();
        const isReady = overlayManager.isLogoImageReady();

        // Debug logo state occasionally
        if (Math.random() < 0.1) { // Log 10% of frames for debugging
          // addLog('debug', 'WebGL', `Logo debug: hasImage=${!!logoImage}, ready=${isReady}`, {
          //   complete: logoImage?.complete,
          //   naturalWidth: logoImage?.naturalWidth,
          //   naturalHeight: logoImage?.naturalHeight,
          //   src: logoImage?.src?.substring(0, 50) + '...'
          // });
        }

        if (!logoImage) {
          if (Math.random() < 0.1) {
            // addLog('warn', 'WebGL', 'No logo image found in overlay manager - trying to setup again');
            // Try to setup logo again
            overlayManager.setupLogoImage();
          }
        } else if (!isReady) {
          if (Math.random() < 0.1) {
            // addLog('warn', 'WebGL', 'Logo image exists but not ready', {
            //   complete: logoImage.complete,
            //   naturalWidth: logoImage.naturalWidth,
            //   naturalHeight: logoImage.naturalHeight
            // });
          }
        }
      } else {
        // addLog('error', 'WebGL', 'No overlay manager found - this should not happen!');
      }

      if (overlayManager && overlayManager.isLogoImageReady()) {
        // addLog('info', 'WebGL', 'Logo is ready, attempting to render');
        const logoImage = overlayManager.getLogoImage()!;
        const logoId = 'logo';

        // Debug logo state occasionally
        if (Math.random() < 0.001) {
          // addLog('debug', 'WebGL', `Logo state: complete=${logoImage.complete}, naturalWidth=${logoImage.naturalWidth}, naturalHeight=${logoImage.naturalHeight}`);
        }

        // Get or create texture for logo image
        let logoTexture = engine.getTexture(logoId);
        if (!logoTexture) {
          const newTexture = engine.createImageTexture(logoId, logoImage);
          if (newTexture) {
            logoTexture = newTexture;
            // addLog('success', 'WebGL', 'Created texture for logo image');
          } else {
            // addLog('error', 'WebGL', 'Failed to create texture for logo image');
          }
        }

        if (logoTexture) {
          try {
            // Calculate logo viewport (top-right, 10% size)
            const logoViewport = calculateLogoViewport(canvas.width, canvas.height);

            // Debug logo viewport occasionally
            if (Math.random() < 0.01) {
              // addLog('debug', 'WebGL', `Logo viewport: x=${logoViewport.x}, y=${logoViewport.y}, w=${logoViewport.width}, h=${logoViewport.height} on canvas ${canvas.width}x${canvas.height}`);
            }

            // Render the logo with no border
            engine.renderVideo(logoTexture, logoViewport, [0, 0, 0], 1.0, 0); // No border, full opacity

            // Log logo rendering success occasionally
            if (Math.random() < 0.001) {
              // addLog('debug', 'WebGL', `Rendered logo at viewport (${logoViewport.x}, ${logoViewport.y}, ${logoViewport.width}, ${logoViewport.height}) on canvas ${canvas.width}x${canvas.height}`);
            }
          } catch (error) {
            // addLog('error', 'WebGL', 'Failed to render logo', error);
          }
        }
      }
    };

    const animate = () => {
      draw();
      requestAnimationFrame(animate);
    };

    animate();

    // Cleanup function
    return () => {
      // Clean up WebGL textures
      if (engine) {
        engine.cleanup();
      }

      // Clean up video manager
      if (videoManagerRef.current) {
        videoManagerRef.current.cleanup();
      }

      // Clean up overlay manager
      if (overlayManagerRef.current) {
        overlayManagerRef.current.cleanup();
      }
    };
  }, [layoutMode, addLog]);

  return (
    <div className="relative bg-gray-900 rounded-xl overflow-hidden">
      {/* Layout Controls */}
      <LayoutControls
        layoutMode={layoutMode}
        onLayoutModeChange={setInternalLayoutMode}
        onDebugVideoState={debugVideoState}
        showOverlays={showOverlays}
      />

      {/* Composite Canvas */}
      <canvas
        ref={canvasRef}
        width={1920}
        height={1080}
        className="w-full aspect-video"
      />

      {/* Status Overlay */}
      <StatusOverlay
        layoutMode={layoutMode}
        peerCount={Object.keys(peers).length}
        hasLocalStream={!!localStream}
        showOverlays={showOverlays}
      />
    </div>
  );
};