import React, { useEffect, useRef, useState } from 'react';
import { LogOut, Settings, Users, Monitor, Mic, MicOff, Video, VideoOff, Share, Grid, Maximize2, Bug } from 'lucide-react';
import { useConnection } from '../contexts/ConnectionContext';
import { VideoCompositor } from './VideoCompositor';
import { RTMPConfig } from './RTMPConfig';

interface HostInterfaceProps {
  roomId: string;
  onLeave: () => void;
}

type LayoutMode = 'grid' | 'focus' | 'pip';

export const HostInterface: React.FC<HostInterfaceProps> = ({ roomId, onLeave }) => {
  const { peers, joinRoom, localStream, toggleAudio, toggleVideo, toggleScreenShare, isAudioEnabled, isVideoEnabled, isScreenSharing } = useConnection();
  const [showSettings, setShowSettings] = useState(false);
  const [rtmpConfig, setRtmpConfig] = useState({ url: '', key: '' });
  const [isStreaming, setIsStreaming] = useState(false);
  const [hasJoined, setHasJoined] = useState(false);
  const [layoutMode, setLayoutMode] = useState<LayoutMode>('grid');
  const localVideoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (!hasJoined) {
      joinRoom(roomId, true);
      setHasJoined(true);
    }
  }, [roomId, joinRoom, hasJoined]);

  useEffect(() => {
    if (localStream && localVideoRef.current) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);

  const handleStartRTMP = () => {
    if (rtmpConfig.url && rtmpConfig.key) {
      setIsStreaming(true);
      // TODO: Implement RTMP streaming logic
      console.log('Starting RTMP stream to:', rtmpConfig.url);
    }
  };

  const handleStopRTMP = () => {
    setIsStreaming(false);
    // TODO: Implement RTMP stop logic
    console.log('Stopping RTMP stream');
  };

  const handleDebugVideoState = () => {
    // This will be called from the VideoCompositor
    console.log('Debug video state requested');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 p-4">
      {/* Header */}
      <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-blue-500/20 p-3 rounded-lg">
              <Monitor className="w-8 h-8 text-blue-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">Host Control Center</h1>
              <p className="text-gray-300">Room ID: <span className="font-mono text-blue-400">{roomId}</span></p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 bg-gray-700/50 rounded-lg px-3 py-2">
              <Users className="w-5 h-5 text-gray-400" />
              <span className="text-white font-semibold">{Object.keys(peers).length + 1}</span>
            </div>
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="bg-gray-700 hover:bg-gray-600 text-white p-3 rounded-lg transition-colors"
            >
              <Settings className="w-5 h-5" />
            </button>
            <button
              onClick={onLeave}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-lg transition-colors flex items-center space-x-2"
            >
              <LogOut className="w-5 h-5" />
              <span>Leave</span>
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* Main Composite View */}
        <div className="xl:col-span-3">
          <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-white">Live Composite</h2>
              <div className="flex items-center space-x-4">
                {/* Layout Controls */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => setLayoutMode('grid')}
                    className={`p-2 rounded-lg transition-colors ${
                      layoutMode === 'grid' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
                    }`}
                    title="Grid Layout"
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setLayoutMode('focus')}
                    className={`p-2 rounded-lg transition-colors ${
                      layoutMode === 'focus' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
                    }`}
                    title="Focus Layout"
                  >
                    <Monitor className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setLayoutMode('pip')}
                    className={`p-2 rounded-lg transition-colors ${
                      layoutMode === 'pip' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
                    }`}
                    title="Picture-in-Picture"
                  >
                    <Maximize2 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={handleDebugVideoState}
                    className="p-2 rounded-lg transition-colors bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30"
                    title="Debug Video State"
                  >
                    <Bug className="w-4 h-4" />
                  </button>
                </div>

                {/* Status Indicator */}
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${isStreaming ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>
                  <span className="text-gray-300">{isStreaming ? 'LIVE' : 'OFFLINE'}</span>
                </div>

                {/* Composite Status */}
                <div className="bg-gray-700/50 rounded-lg px-3 py-1">
                  <span className="text-white text-sm">
                    Layout: {layoutMode.toUpperCase()} • {Object.keys(peers).length} participants • {localStream ? 'Host' : 'No host'}
                  </span>
                </div>
              </div>
            </div>
            <VideoCompositor
              peers={peers}
              localStream={localStream}
              overlayVideoUrl="https://www.switcherstudio.com/hubfs/Homepage%20Video%20Hero/homepage_hero_desktop_rev.webm"
              showOverlays={false}
              layoutMode={layoutMode}
              onLayoutModeChange={setLayoutMode}
              onDebugVideoState={handleDebugVideoState}
            />
          </div>
        </div>

        {/* Control Panel */}
        <div className="space-y-6">
          {/* Local Preview */}
          <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-4">
            <h3 className="text-lg font-bold text-white mb-3">Your Preview</h3>
            <div className="relative bg-gray-900 rounded-lg overflow-hidden aspect-video">
              <video
                ref={localVideoRef}
                autoPlay
                muted
                playsInline
                className="w-full h-full object-cover"
              />
              <div className="absolute bottom-2 left-2 flex space-x-2">
                <button
                  onClick={toggleAudio}
                  className={`p-2 rounded-lg transition-colors ${
                    isAudioEnabled ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                  }`}
                >
                  {isAudioEnabled ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
                </button>
                <button
                  onClick={toggleVideo}
                  className={`p-2 rounded-lg transition-colors ${
                    isVideoEnabled ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                  }`}
                >
                  {isVideoEnabled ? <Video className="w-4 h-4" /> : <VideoOff className="w-4 h-4" />}
                </button>
                <button
                  onClick={toggleScreenShare}
                  className={`p-2 rounded-lg transition-colors ${
                    isScreenSharing ? 'bg-blue-500/20 text-blue-400' : 'bg-gray-500/20 text-gray-400'
                  }`}
                  title={isScreenSharing ? 'Stop Screen Share' : 'Start Screen Share'}
                >
                  <Share className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Participants */}
          <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-4">
            <h3 className="text-lg font-bold text-white mb-3">Participants</h3>
            <div className="space-y-2">
              {Object.entries(peers).map(([peerId]) => (
                <div key={peerId} className="flex items-center justify-between bg-gray-700/50 rounded-lg p-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                      <Users className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-white">{peerId.substring(0, 8)}...</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-green-400 text-sm">Connected</span>
                  </div>
                </div>
              ))}
              {Object.keys(peers).length === 0 && (
                <p className="text-gray-400 text-center py-4">No participants yet</p>
              )}
            </div>
          </div>

          {/* RTMP Streaming */}
          {showSettings && (
            <RTMPConfig
              config={rtmpConfig}
              onChange={setRtmpConfig}
              isStreaming={isStreaming}
              onStart={handleStartRTMP}
              onStop={handleStopRTMP}
            />
          )}
        </div>
      </div>
    </div>
  );
};