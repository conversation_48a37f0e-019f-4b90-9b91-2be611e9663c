/**
 * WebGL 2.0 shaders for video compositing
 */

export const vertexShaderSource = `#version 300 es
in vec2 a_position;
in vec2 a_texCoord;
out vec2 v_texCoord;

void main() {
  gl_Position = vec4(a_position, 0.0, 1.0);
  v_texCoord = a_texCoord;
}`;

export const fragmentShaderSource = `#version 300 es
precision mediump float;

in vec2 v_texCoord;
out vec4 fragColor;

uniform sampler2D u_texture;
uniform float u_alpha;
uniform vec2 u_resolution;
uniform vec4 u_viewport; // x, y, width, height in normalized coordinates
uniform vec3 u_borderColor;
uniform float u_borderWidth;

void main() {
  // Check if we're in the viewport
  vec2 normalizedCoord = gl_FragCoord.xy / u_resolution;

  if (normalizedCoord.x < u_viewport.x ||
      normalizedCoord.x > u_viewport.x + u_viewport.z ||
      normalizedCoord.y < u_viewport.y ||
      normalizedCoord.y > u_viewport.y + u_viewport.w) {
    fragColor = vec4(0.0, 0.0, 0.0, 0.0); // Transparent background for proper compositing
    return;
  }

  // Calculate texture coordinates within the viewport
  vec2 viewportCoord = (normalizedCoord - u_viewport.xy) / u_viewport.zw;

  // Flip Y coordinate to fix vertical inversion
  viewportCoord.y = 1.0 - viewportCoord.y;

  // Sample the texture
  vec4 texColor = texture(u_texture, viewportCoord);

  // Add border effect
  vec2 borderDist = min(viewportCoord, 1.0 - viewportCoord);
  float borderFactor = min(borderDist.x, borderDist.y);

  if (borderFactor < u_borderWidth) {
    float borderAlpha = smoothstep(0.0, u_borderWidth, borderFactor);
    fragColor = vec4(mix(u_borderColor, texColor.rgb, borderAlpha), texColor.a * u_alpha);
  } else {
    fragColor = vec4(texColor.rgb, texColor.a * u_alpha);
  }
}`;
