import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useLog } from './LogContext';

interface Peer {
  id: string;
  connection: RTCPeerConnection;
  stream?: MediaStream;
}

interface ConnectionContextType {
  peersRef: React.RefObject<Record<string, Peer>>;
  peerIds: string[];
  localStream: MediaStream | null;
  joinRoom: (roomId: string, isHost: boolean) => Promise<void>;
  leaveRoom: () => void;
  toggleAudio: () => void;
  toggleVideo: () => void;
  toggleScreenShare: () => Promise<void>;
  isAudioEnabled: boolean;
  isVideoEnabled: boolean;
  isScreenSharing: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected';
}

const ConnectionContext = createContext<ConnectionContextType | null>(null);

export const useConnection = () => {
  const context = useContext(ConnectionContext);
  if (!context) {
    throw new Error('useConnection must be used within a ConnectionProvider');
  }
  return context;
};

export const ConnectionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [peerIds, setPeerIds] = useState<string[]>([]);
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');

  // Use refs to avoid stale closure issues and prevent infinite loops
  const peersRef = useRef<Record<string, Peer>>({});
  const socketRef = useRef<Socket | null>(null);
  const localStreamRef = useRef<MediaStream | null>(null);
  const connectionStatusRef = useRef<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const isJoiningRef = useRef(false);
  const currentRoomRef = useRef<string | null>(null);
  const { addLog } = useLog();

  // Helper function to add a peer and trigger re-render
  const addPeer = useCallback((peer: Peer) => {
    peersRef.current[peer.id] = peer;
    setPeerIds(Object.keys(peersRef.current)); // Trigger re-render
  }, []);

  // Helper function to remove a peer and trigger re-render
  const removePeer = useCallback((peerId: string) => {
    if (peersRef.current[peerId]) {
      delete peersRef.current[peerId];
      setPeerIds(Object.keys(peersRef.current)); // Trigger re-render
    }
  }, []);

  // Helper function to update a peer and trigger re-render
  const updatePeer = useCallback((peerId: string, updates: Partial<Peer>) => {
    if (peersRef.current[peerId]) {
      peersRef.current[peerId] = { ...peersRef.current[peerId], ...updates };
      setPeerIds(Object.keys(peersRef.current)); // Trigger re-render
    }
  }, []);

  useEffect(() => {
    socketRef.current = socket;
  }, [socket]);

  useEffect(() => {
    connectionStatusRef.current = connectionStatus;
  }, [connectionStatus]);

  useEffect(() => {
    localStreamRef.current = localStream;

    // Add tracks to existing peer connections when local stream becomes available
    if (localStream) {
      const currentPeers = peersRef.current;
      Object.entries(currentPeers).forEach(([peerId, peer]) => {
        const existingSenders = peer.connection.getSenders();
        const localTracks = localStream.getTracks();

        // Check if we need to add tracks
        localTracks.forEach(track => {
          const existingSender = existingSenders.find(sender =>
            sender.track && sender.track.kind === track.kind
          );

          if (!existingSender) {
            addLog('info', 'WebRTC', `Adding ${track.kind} track to existing peer ${peerId}`);
            peer.connection.addTrack(track, localStream);
          }
        });
      });
    }
  }, [localStream, addLog]);

  // Get TURN server configuration from environment or use defaults
  const getTurnServerConfig = () => {
    const turnServer = import.meta.env.VITE_TURN_SERVER;
    const turnUsername = import.meta.env.VITE_TURN_USERNAME || 'webrtc';
    const turnPassword = import.meta.env.VITE_TURN_PASSWORD || 'webrtc123';

    const iceServers = [
      // STUN servers for NAT discovery
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun2.l.google.com:19302' },
      { urls: 'stun:stun3.l.google.com:19302' },
      { urls: 'stun:stun4.l.google.com:19302' },
    ];

    // Add custom TURN server if configured
    if (turnServer) {
      console.log(`🔄 Using custom TURN server: ${turnServer}`);
      iceServers.push(
        {
          urls: `turn:${turnServer}:3478`,
          username: turnUsername,
          credential: turnPassword
        },
        {
          urls: `turn:${turnServer}:3478?transport=tcp`,
          username: turnUsername,
          credential: turnPassword
        }
      );
    }

    // Add Metered TURN service as fallback
    iceServers.push(
      {
        urls: 'turn:a.relay.metered.ca:80',
        username: 'e8348eb4d21d62d8d13a6a44',
        credential: 'Bj+6xJqZzk7EYlut'
      },
      {
        urls: 'turn:a.relay.metered.ca:80?transport=tcp',
        username: 'e8348eb4d21d62d8d13a6a44',
        credential: 'Bj+6xJqZzk7EYlut'
      },
      {
        urls: 'turn:a.relay.metered.ca:443',
        username: 'e8348eb4d21d62d8d13a6a44',
        credential: 'Bj+6xJqZzk7EYlut'
      },
      {
        urls: 'turn:a.relay.metered.ca:443?transport=tcp',
        username: 'e8348eb4d21d62d8d13a6a44',
        credential: 'Bj+6xJqZzk7EYlut'
      }
    );

    return iceServers;
  };

  const rtcConfig = {
    iceServers: getTurnServerConfig(),
    iceCandidatePoolSize: 10,
    iceTransportPolicy: 'relay' as RTCIceTransportPolicy // Force TURN relay only for testing
  };

  const createPeerConnection = useCallback((peerId: string, isInitiator: boolean) => {
    // Check if peer connection already exists
    const existingPeer = peersRef.current[peerId];
    if (existingPeer) {
      addLog('warn', 'WebRTC', `Peer connection already exists for ${peerId}, returning existing connection`);
      return existingPeer.connection;
    }

    addLog('info', 'WebRTC', `Creating peer connection for ${peerId} (initiator: ${isInitiator})`, {
      stunServers: rtcConfig.iceServers.filter(server => server.urls.toString().includes('stun')).length,
      turnServers: rtcConfig.iceServers.filter(server => server.urls.toString().includes('turn')).length,
      iceCandidatePoolSize: rtcConfig.iceCandidatePoolSize
    });

    const pc = new RTCPeerConnection(rtcConfig);

    pc.onicecandidate = (event) => {
      if (event.candidate && socket) {
        // Determine candidate type for logging
        const candidateStr = event.candidate.candidate;
        let candidateType = 'unknown';
        if (candidateStr.includes('typ host')) candidateType = 'host';
        else if (candidateStr.includes('typ srflx')) candidateType = 'srflx (STUN)';
        else if (candidateStr.includes('typ relay')) candidateType = 'relay (TURN)';
        else if (candidateStr.includes('typ prflx')) candidateType = 'prflx';

        // Enhanced logging for TURN debugging
        const isRelay = candidateStr.includes('typ relay');
        const logLevel = isRelay ? 'success' : 'warn';
        const prefix = isRelay ? '🔄 TURN' : '⚠️ NON-TURN';

        addLog(logLevel, 'ICE', `🧊 ${prefix} ${candidateType} candidate to ${peerId}`, {
          candidate: event.candidate.candidate,
          type: candidateType,
          protocol: event.candidate.protocol,
          address: event.candidate.address,
          port: event.candidate.port,
          priority: event.candidate.priority,
          foundation: event.candidate.foundation,
          component: event.candidate.component,
          relatedAddress: event.candidate.relatedAddress,
          relatedPort: event.candidate.relatedPort
        });

        // If we're forcing relay but getting non-relay candidates, that's a problem
        if (!isRelay && rtcConfig.iceTransportPolicy === 'relay') {
          addLog('error', 'ICE', `❌ Non-relay candidate generated despite iceTransportPolicy: 'relay'!`, {
            candidateType,
            candidateString: candidateStr
          });
        }

        socket.emit('ice-candidate', {
          candidate: event.candidate,
          to: peerId,
        });
      } else if (!event.candidate) {
        addLog('debug', 'ICE', `ICE gathering complete for ${peerId}`);
      }
    };

    pc.ontrack = (event) => {
      const [remoteStream] = event.streams;
      addLog('success', 'WebRTC', `Received remote stream from ${peerId}`, {
        streamId: remoteStream.id,
        tracks: remoteStream.getTracks().map(t => ({ kind: t.kind, id: t.id }))
      });

      const currentPeer = peersRef.current[peerId];
      if (!currentPeer) {
        addLog('warn', 'WebRTC', `Received stream for unknown peer ${peerId}, creating peer object`);
        addPeer({
          id: peerId,
          connection: pc,
          stream: remoteStream,
        });
      } else {
        addLog('info', 'WebRTC', `Updating existing peer ${peerId} with stream`);
        updatePeer(peerId, { stream: remoteStream });
      }
    };

    pc.onconnectionstatechange = () => {
      addLog('info', 'WebRTC', `Peer ${peerId} connection state: ${pc.connectionState}`, {
        currentGlobalStatus: connectionStatusRef.current,
        iceConnectionState: pc.iceConnectionState,
        signalingState: pc.signalingState
      });

      // Log critical connection states
      if (pc.connectionState === 'connected') {
        addLog('success', 'WebRTC', `🎉 Peer ${peerId} fully connected!`);
      } else if (pc.connectionState === 'failed') {
        addLog('error', 'WebRTC', `❌ Peer ${peerId} connection failed!`);
      } else if (pc.connectionState === 'disconnected') {
        addLog('warn', 'WebRTC', `⚠️ Peer ${peerId} disconnected`);
      }

      // Update global connection status based on peer connections
      if (pc.connectionState === 'connected') {
        setConnectionStatus('connected');
        addLog('success', 'WebRTC', `Successfully connected to peer ${peerId} - updating global status to connected`);
      } else if (pc.connectionState === 'failed') {
        addLog('error', 'WebRTC', `Connection failed to peer ${peerId}`);
        // Check if we have any other connected peers before setting to disconnected
        const currentPeers = peersRef.current;
        const hasConnectedPeers = Object.values(currentPeers).some(peer =>
          peer.connection.connectionState === 'connected'
        );
        if (!hasConnectedPeers) {
          setConnectionStatus('disconnected');
        }
      } else if (pc.connectionState === 'disconnected') {
        addLog('warn', 'WebRTC', `Disconnected from peer ${peerId}`);
        // Check if we have any other connected peers before setting to disconnected
        const currentPeers = peersRef.current;
        const hasConnectedPeers = Object.values(currentPeers).some(peer =>
          peer.connection.connectionState === 'connected'
        );
        if (!hasConnectedPeers) {
          setConnectionStatus('disconnected');
        }
      } else if (pc.connectionState === 'connecting') {
        // Keep status as connecting if we're still trying to connect
        if (connectionStatusRef.current !== 'connected') {
          setConnectionStatus('connecting');
          addLog('debug', 'WebRTC', `Peer ${peerId} connecting - maintaining connecting status`);
        }
      }
    };

    pc.onicegatheringstatechange = () => {
      addLog('info', 'ICE', `🔄 ICE gathering state for ${peerId}: ${pc.iceGatheringState}`);

      if (pc.iceGatheringState === 'complete') {
        // Log summary of gathered candidates
        addLog('success', 'ICE', `✅ ICE gathering complete for ${peerId}`);
      }
    };

    pc.oniceconnectionstatechange = () => {
      addLog('info', 'ICE', `ICE connection state for ${peerId}: ${pc.iceConnectionState}`, {
        connectionState: pc.connectionState,
        signalingState: pc.signalingState,
        currentGlobalStatus: connectionStatusRef.current
      });

      // ICE connection state is often more reliable than connection state
      if (pc.iceConnectionState === 'connected' || pc.iceConnectionState === 'completed') {
        setConnectionStatus('connected');
        addLog('success', 'ICE', `ICE connection established for peer ${peerId} - updating global status to connected`);
      } else if (pc.iceConnectionState === 'failed') {
        addLog('error', 'ICE', `ICE connection failed for peer ${peerId}`);
        // Check if we have any other connected peers
        const currentPeers = peersRef.current;
        const hasConnectedPeers = Object.values(currentPeers).some(peer =>
          peer.connection.iceConnectionState === 'connected' ||
          peer.connection.iceConnectionState === 'completed'
        );
        if (!hasConnectedPeers) {
          setConnectionStatus('disconnected');
        }
      } else if (pc.iceConnectionState === 'disconnected') {
        addLog('warn', 'ICE', `ICE connection disconnected for peer ${peerId}`);
      } else if (pc.iceConnectionState === 'checking') {
        addLog('debug', 'ICE', `ICE connection checking for peer ${peerId}`);
      }
    };

    pc.onsignalingstatechange = () => {
      addLog('debug', 'WebRTC', `Signaling state for ${peerId}: ${pc.signalingState}`);
    };

    // Add local stream tracks - use ref to get current stream
    const currentLocalStream = localStreamRef.current;
    if (currentLocalStream) {
      const tracks = currentLocalStream.getTracks();
      addLog('info', 'WebRTC', `Adding ${tracks.length} local tracks to peer ${peerId}`, {
        tracks: tracks.map(t => ({ kind: t.kind, id: t.id }))
      });

      tracks.forEach(track => {
        pc.addTrack(track, currentLocalStream);
      });
    } else {
      addLog('warn', 'WebRTC', `No local stream available when creating peer connection for ${peerId} - tracks will be added later`);
    }

    const newPeer = {
      id: peerId,
      connection: pc,
    };

    addLog('info', 'WebRTC', `Created peer object for ${peerId}`, {
      existingPeers: Object.keys(peersRef.current),
      newPeerId: peerId
    });

    addPeer(newPeer);

    return pc;
  }, [socket, addLog]);

  const joinRoom = useCallback(async (roomId: string, isHost: boolean) => {
    // Prevent multiple simultaneous join attempts
    if (isJoiningRef.current) {
      addLog('warn', 'Room', 'Already joining a room, ignoring duplicate request');
      return;
    }

    // Check if already in the same room
    if (currentRoomRef.current === roomId && socketRef.current?.connected) {
      addLog('warn', 'Room', `Already connected to room ${roomId}`);
      return;
    }

    try {
      isJoiningRef.current = true;
      addLog('info', 'Room', `Attempting to join room ${roomId} as ${isHost ? 'host' : 'participant'}`);
      setConnectionStatus('connecting');

      // Clean up any existing connection first
      if (socketRef.current) {
        addLog('info', 'Socket', 'Cleaning up existing connection...');
        socketRef.current.disconnect();
        setSocket(null);
      }

      // Get user media only if we don't have it already
      let stream = localStreamRef.current;
      if (!stream) {
        addLog('info', 'Media', 'Requesting user media access...');
        stream = await navigator.mediaDevices.getUserMedia({
          video: { width: 1280, height: 720 },
          audio: { echoCancellation: true, noiseSuppression: true },
        });

        addLog('success', 'Media', 'User media access granted', {
          videoTracks: stream.getVideoTracks().length,
          audioTracks: stream.getAudioTracks().length
        });

        // Update both state and ref immediately to ensure availability for peer connections
        setLocalStream(stream);
        localStreamRef.current = stream;
      } else {
        addLog('info', 'Media', 'Using existing media stream');
      }

      // Connect to signaling server
      addLog('info', 'Socket', 'Connecting to signaling server...');
      // if running on localhost, use http://localhost:3001, otherwise undefined
      const newSocket = io(window.location.hostname === 'localhost' ? 'http://localhost:3001' : undefined, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true
      });
      setSocket(newSocket);
      currentRoomRef.current = roomId;

      // Add connection error handling
      newSocket.on('connect_error', (error) => {
        addLog('error', 'Socket', `Connection error: ${error.message}`);
        setConnectionStatus('disconnected');
        isJoiningRef.current = false;
      });

      newSocket.on('reconnect_error', (error) => {
        addLog('error', 'Socket', `Reconnection error: ${error.message}`);
      });

      newSocket.on('connect', () => {
        addLog('success', 'Socket', `Connected to signaling server with ID: ${newSocket.id}`);
        newSocket.emit('join-room', { roomId, isHost });
        addLog('info', 'Room', `Sent join-room request for ${roomId}`);
        isJoiningRef.current = false; // Mark joining as complete
      });

      newSocket.on('user-joined', ({ peerId }) => {
        addLog('info', 'Room', `User joined room: ${peerId}`);
        addLog('debug', 'Room', `Current local stream available: ${!!localStreamRef.current}`);
        // Don't create peer connection here - wait for initiate-connection signal
      });

      newSocket.on('initiate-connection', ({ peerId }) => {
        addLog('info', 'WebRTC', `Initiating connection to ${peerId}`);
        addLog('debug', 'WebRTC', `Local stream available for initiation: ${!!localStreamRef.current}`);

        // Create the peer connection as initiator
        const pc = createPeerConnection(peerId, true);
        
        pc.createOffer().then(offer => {
          return pc.setLocalDescription(offer);
        }).then(() => {
          addLog('success', 'WebRTC', `Created and set local offer for ${peerId}`);
          newSocket.emit('offer', {
            offer: pc.localDescription,
            to: peerId,
          });
        }).catch(error => {
          const errorMessage = error instanceof Error ? error.message : String(error);
          addLog('error', 'WebRTC', `Failed to create offer for ${peerId}`, {
            error: errorMessage,
            signalingState: pc.signalingState
          });
        });
      });

      newSocket.on('offer', async ({ offer, from }) => {
        addLog('info', 'WebRTC', `Received offer from: ${from}`);
        addLog('debug', 'WebRTC', `Local stream available for offer handling: ${!!localStreamRef.current}`);

        // Create peer connection when receiving offer (as non-initiator)
        const pc = createPeerConnection(from, false);

        try {
          await pc.setRemoteDescription(offer);
          addLog('success', 'WebRTC', `Set remote description (offer) from ${from}`);

          const answer = await pc.createAnswer();
          await pc.setLocalDescription(answer);
          addLog('success', 'WebRTC', `Created and set local answer for ${from}`);

          newSocket.emit('answer', {
            answer: pc.localDescription,
            to: from,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          addLog('error', 'WebRTC', `Failed to handle offer from ${from}`, {
            error: errorMessage,
            signalingState: pc.signalingState
          });
        }
      });

      newSocket.on('answer', async ({ answer, from }) => {
        addLog('info', 'WebRTC', `Received answer from: ${from}`);

        // Use ref to get current peers to avoid stale closure
        const peer = peersRef.current[from];
        if (peer) {
          try {
            addLog('debug', 'WebRTC', `Setting remote description (answer) from ${from}`, {
              answerType: answer.type,
              answerSdp: answer.sdp?.substring(0, 100) + '...',
              currentSignalingState: peer.connection.signalingState,
              currentConnectionState: peer.connection.connectionState
            });

            // Check if we're in the right state to set remote description
            if (peer.connection.signalingState === 'stable') {
              addLog('warn', 'WebRTC', `Peer connection already stable when receiving answer from ${from}, ignoring duplicate answer`);
              return; // Skip setting the answer if already stable
            } else if (peer.connection.signalingState !== 'have-local-offer') {
              addLog('warn', 'WebRTC', `Unexpected signaling state ${peer.connection.signalingState} when setting answer from ${from}, expected 'have-local-offer'`);
            }

            await peer.connection.setRemoteDescription(answer);
            addLog('success', 'WebRTC', `Successfully processed answer from ${from}`);
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            addLog('error', 'WebRTC', `Failed to set remote description from ${from}`, {
              error: errorMessage,
              stack: errorStack,
              answerType: answer?.type,
              signalingState: peer.connection.signalingState,
              connectionState: peer.connection.connectionState
            });
          }
        } else {
          addLog('error', 'WebRTC', `No peer connection found for ${from} when processing answer`);
        }
      });

      newSocket.on('ice-candidate', async ({ candidate, from }) => {
        addLog('debug', 'ICE', `Received ICE candidate from: ${from}`, {
          candidate: candidate.candidate,
          type: candidate.type
        });

        // Use ref to get current peers to avoid stale closure
        const peer = peersRef.current[from];
        if (peer) {
          try {
            await peer.connection.addIceCandidate(candidate);
            addLog('debug', 'ICE', `Successfully added ICE candidate from ${from}`);
          } catch (error) {
            addLog('error', 'ICE', `Failed to add ICE candidate from ${from}`, error);
          }
        } else {
          addLog('error', 'ICE', `No peer connection found for ${from} when processing ICE candidate`);
        }
      });

      newSocket.on('user-left', ({ peerId }) => {
        addLog('info', 'Room', `User left: ${peerId}`);
        const peer = peersRef.current[peerId];
        if (peer) {
          peer.connection.close();
          removePeer(peerId);
          addLog('info', 'WebRTC', `Closed connection to ${peerId}`);
        }
      });

      newSocket.on('error', ({ message }) => {
        addLog('error', 'Socket', `Server error: ${message}`);
      });

      newSocket.on('disconnect', () => {
        addLog('warn', 'Socket', 'Disconnected from signaling server');
        currentRoomRef.current = null;
      });

    } catch (error) {
      addLog('error', 'Room', 'Failed to join room', error);
      setConnectionStatus('disconnected');
      isJoiningRef.current = false;
      currentRoomRef.current = null;
    }
  }, [createPeerConnection, addLog]);

  const leaveRoom = useCallback(() => {
    addLog('info', 'Room', 'Leaving room...');

    // Reset joining flag
    isJoiningRef.current = false;

    if (socketRef.current) {
      socketRef.current.disconnect();
      setSocket(null);
      addLog('info', 'Socket', 'Disconnected from signaling server');
    }

    const currentPeers = peersRef.current;
    const peerCount = Object.keys(currentPeers).length;
    Object.values(currentPeers).forEach(peer => {
      peer.connection.close();
    });

    // Clear all peers
    peersRef.current = {};
    setPeerIds([]);

    if (peerCount > 0) {
      addLog('info', 'WebRTC', `Closed ${peerCount} peer connections`);
    }

    if (localStreamRef.current) {
      const trackCount = localStreamRef.current.getTracks().length;
      localStreamRef.current.getTracks().forEach(track => track.stop());
      setLocalStream(null);
      addLog('info', 'Media', `Stopped ${trackCount} local media tracks`);
    }

    currentRoomRef.current = null;
    setConnectionStatus('disconnected');
    addLog('success', 'Room', 'Successfully left room');
  }, [addLog]);

  const toggleAudio = useCallback(() => {
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsAudioEnabled(audioTrack.enabled);
        addLog('info', 'Media', `Audio ${audioTrack.enabled ? 'enabled' : 'disabled'}`);
      } else {
        addLog('warn', 'Media', 'No audio track found to toggle');
      }
    } else {
      addLog('warn', 'Media', 'No local stream available to toggle audio');
    }
  }, [localStream, addLog]);

  const toggleVideo = useCallback(() => {
    if (localStream) {
      const videoTrack = localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsVideoEnabled(videoTrack.enabled);
        addLog('info', 'Media', `Video ${videoTrack.enabled ? 'enabled' : 'disabled'}`);
      } else {
        addLog('warn', 'Media', 'No video track found to toggle');
      }
    } else {
      addLog('warn', 'Media', 'No local stream available to toggle video');
    }
  }, [localStream, addLog]);

  const toggleScreenShare = useCallback(async () => {
    try {
      if (isScreenSharing) {
        // Stop screen sharing, switch back to camera
        addLog('info', 'Media', 'Stopping screen share, switching to camera...');

        // Get camera stream
        const cameraStream = await navigator.mediaDevices.getUserMedia({
          video: { width: 1280, height: 720 },
          audio: { echoCancellation: true, noiseSuppression: true },
        });

        // Replace tracks in peer connections
        const currentPeers = peersRef.current;
        Object.values(currentPeers).forEach(peer => {
          const senders = peer.connection.getSenders();
          const videoSender = senders.find(sender => sender.track?.kind === 'video');
          const audioSender = senders.find(sender => sender.track?.kind === 'audio');

          if (videoSender && cameraStream.getVideoTracks()[0]) {
            videoSender.replaceTrack(cameraStream.getVideoTracks()[0]);
          }
          if (audioSender && cameraStream.getAudioTracks()[0]) {
            audioSender.replaceTrack(cameraStream.getAudioTracks()[0]);
          }
        });

        // Stop old stream
        if (localStreamRef.current) {
          localStreamRef.current.getTracks().forEach(track => track.stop());
        }

        // Update stream
        setLocalStream(cameraStream);
        localStreamRef.current = cameraStream;
        setIsScreenSharing(false);
        addLog('success', 'Media', 'Switched back to camera');

      } else {
        // Start screen sharing
        addLog('info', 'Media', 'Starting screen share...');

        const screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: { width: 1920, height: 1080 },
          audio: true
        });

        // Add audio from microphone if screen doesn't have audio
        if (screenStream.getAudioTracks().length === 0 && localStreamRef.current) {
          const audioTracks = localStreamRef.current.getAudioTracks();
          if (audioTracks.length > 0) {
            screenStream.addTrack(audioTracks[0]);
          }
        }

        // Replace tracks in peer connections
        const currentPeers = peersRef.current;
        Object.values(currentPeers).forEach(peer => {
          const senders = peer.connection.getSenders();
          const videoSender = senders.find(sender => sender.track?.kind === 'video');
          const audioSender = senders.find(sender => sender.track?.kind === 'audio');

          if (videoSender && screenStream.getVideoTracks()[0]) {
            videoSender.replaceTrack(screenStream.getVideoTracks()[0]);
          }
          if (audioSender && screenStream.getAudioTracks()[0]) {
            audioSender.replaceTrack(screenStream.getAudioTracks()[0]);
          }
        });

        // Handle screen share ending (user clicks "Stop sharing" in browser)
        screenStream.getVideoTracks()[0].addEventListener('ended', () => {
          addLog('info', 'Media', 'Screen share ended by user');
          toggleScreenShare(); // This will switch back to camera
        });

        // Stop old stream
        if (localStreamRef.current) {
          localStreamRef.current.getTracks().forEach(track => track.stop());
        }

        // Update stream
        setLocalStream(screenStream);
        localStreamRef.current = screenStream;
        setIsScreenSharing(true);
        addLog('success', 'Media', 'Screen sharing started');
      }
    } catch (error) {
      addLog('error', 'Media', 'Failed to toggle screen share', error);
    }
  }, [isScreenSharing, addLog]);

  // Cleanup on unmount only - don't include leaveRoom in dependencies to avoid infinite loop
  useEffect(() => {
    return () => {
      // Use refs for cleanup to avoid stale closures
      if (socketRef.current) {
        socketRef.current.disconnect();
      }

      Object.values(peersRef.current).forEach(peer => {
        peer.connection.close();
      });

      if (localStreamRef.current) {
        localStreamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []); // Empty dependency array - only run on mount/unmount

  return (
    <ConnectionContext.Provider
      value={{
        peersRef,
        peerIds,
        localStream,
        joinRoom,
        leaveRoom,
        toggleAudio,
        toggleVideo,
        toggleScreenShare,
        isAudioEnabled,
        isVideoEnabled,
        isScreenSharing,
        connectionStatus,
      }}
    >
      {children}
    </ConnectionContext.Provider>
  );
};
